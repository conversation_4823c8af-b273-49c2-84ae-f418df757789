import { Routes, Route } from 'react-router-dom'
import { lazy, Suspense } from 'react'

// 懒加载页面组件 - 指向修改后的 app 目录下的文件
const HomePage = lazy(() => import('@/app/page'))
const LoginPage = lazy(() => import('@/app/login/page'))
const DashboardPage = lazy(() => import('@/app/dashboard/page'))
const ForgotPasswordPage = lazy(() => import('@/app/forgot-password/page'))

// 设备管理相关页面
const DevicesPage = lazy(() => import('@/app/devices/page'))
const DeviceAddPage = lazy(() => import('@/app/devices/add/page'))
const DeviceEditPage = lazy(() => import('@/app/devices/edit/[id]/page'))
const DeviceDetailsPage = lazy(() => import('@/app/devices/[id]/details/page'))
const DeviceTagsPage = lazy(() => import('@/app/devices/[id]/tags/page'))
const DeviceMessagesPage = lazy(
  () => import('@/app/devices/[id]/messages/page')
)
const DeviceLogsPage = lazy(() => import('@/app/devices/[id]/logs/page'))
const DeviceWriteTagsPage = lazy(
  () => import('@/app/devices/[id]/write-tags/page')
)
const AllTagsPage = lazy(() => import('@/app/devices/tags/page'))
const DeviceAlarmsPage = lazy(() => import('@/app/devices/alarms/page'))
const DeviceEventsPage = lazy(() => import('@/app/devices/events/page'))
const DeviceGroupsPage = lazy(() => import('@/app/devices/groups/page'))
const DeviceTemplatesPage = lazy(() => import('@/app/devices/templates/page'))
const DataCollectionPage = lazy(
  () => import('@/app/devices/data-collection/page')
)
const AILabelDemoPage = lazy(() => import('@/app/devices/ai-demo/page'))

// 数据转发相关页面
const DataForwardingPage = lazy(() => import('@/app/data-forwarding/page'))
const DataForwardingAddPage = lazy(
  () => import('@/app/data-forwarding/add/page')
)
const DataForwardingDetailPage = lazy(
  () => import('@/app/data-forwarding/[id]/page')
)
const OfflineDataPage = lazy(
  () => import('@/app/data-forwarding/offline-data/page')
)

// 系统设置相关页面
const SettingsPage = lazy(() => import('@/app/settings/page'))
const SettingsAuthPage = lazy(() => import('@/app/settings/auth/page'))
const SettingsConfigPage = lazy(() => import('@/app/settings/config/page'))
const SettingsParamsPage = lazy(() => import('@/app/settings/params/page'))
const SettingsUpdatePage = lazy(() => import('@/app/settings/update/page'))
const SettingsNetworkPage = lazy(() => import('@/app/settings/network/page'))
const SettingsServicesPage = lazy(() => import('@/app/settings/services/page'))
const SettingsInfoPage = lazy(() => import('@/app/settings/info/page'))
const SettingsAlertsPage = lazy(() => import('@/app/settings/alerts/page'))
const SettingsBrandingPage = lazy(() => import('@/app/settings/branding/page'))
const SettingsLogsPage = lazy(() => import('@/app/settings/logs/page'))

// 系统管理相关页面
const SystemPage = lazy(() => import('@/app/system/page'))
const SystemUsersPage = lazy(() => import('@/app/system/users/page'))
const SystemRolesPage = lazy(() => import('@/app/system/roles/page'))
const SystemMenusPage = lazy(() => import('@/app/system/menus/page'))
const SystemPermissionsPage = lazy(() => import('@/app/system/permissions/page'))
const SystemOnlineUsersPage = lazy(() => import('@/app/system/online-users/page'))
const SystemLogsPage = lazy(() => import('@/app/system/logs/page'))
const PermissionsSimplePage = lazy(() => import('@/app/system/permissions-simple/page'))
const OnlineUsersSimplePage = lazy(() => import('@/app/system/online-users-simple/page'))
const LogsSimplePage = lazy(() => import('@/app/system/logs-simple/page'))

// 个人资料、许可证和帮助页面
const ProfilePage = lazy(() => import('@/app/profile/page'))
const LicensePage = lazy(() => import('@/app/license/page'))
const HelpPage = lazy(() => import('@/app/help/page'))

// 工作流编排相关页面
const WorkflowsPage = lazy(() => import('@/app/workflows/page'))
const WorkflowNewPage = lazy(() => import('@/app/workflows/new/page'))
const WorkflowEditorPage = lazy(() => import('@/app/workflows/editor/page'))
const WorkflowBlankPage = lazy(() => import('@/app/workflows/blank/page'))
const WorkflowMonitoringPage = lazy(
  () => import('@/app/workflows/monitoring/page')
)
const WorkflowTemplatesPage = lazy(
  () => import('@/app/workflows/templates/page')
)

// 监控相关页面
const MonitoringPage = lazy(() => import('@/app/monitoring/page'))
const MonitoringDatabasePage = lazy(() => import('@/app/monitoring/database/page'))

// 任务中心相关页面
const TaskCenterPage = lazy(() => import('@/app/task-center/page'))
const TaskCenterHistoryPage = lazy(() => import('@/app/task-center/history/page'))
const TaskCenterScheduledTasksPage = lazy(() => import('@/app/task-center/scheduled-tasks/page'))
const TaskCenterTaskConfigPage = lazy(() => import('@/app/task-center/task-config/page'))
const TaskCenterMonitoringPage = lazy(() => import('@/app/task-center/monitoring/page'))

// 调试工具相关页面
const DebugToolsPage = lazy(() => import('@/app/debug-tools/page'))
const HttpTestPage = lazy(() => import('@/app/debug-tools/http-test/page'))
const PingTestPage = lazy(() => import('@/app/debug-tools/ping-test/page'))
const PortTestPage = lazy(() => import('@/app/debug-tools/port-test/page'))
const PacketCapturePage = lazy(
  () => import('@/app/debug-tools/packet-capture/page')
)
const TcpTestPage = lazy(() => import('@/app/debug-tools/tcp-test/page'))
const SerialDebugPage = lazy(
  () => import('@/app/debug-tools/serial-debug/page')
)
const MqttTestPage = lazy(() => import('@/app/debug-tools/mqtt-test/page'))
const WebSocketTestPage = lazy(
  () => import('@/app/debug-tools/websocket-test/page')
)
const WebSSHPage = lazy(() => import('@/app/debug-tools/webssh/page'))
const ProtocolAnalysisPage = lazy(
  () => import('@/app/debug-tools/protocol-analysis/page')
)
const JsonFormatterPage = lazy(
  () => import('@/app/debug-tools/json-formatter/page')
)
const TimestampConverterPage = lazy(
  () => import('@/app/debug-tools/timestamp-converter/page')
)
const DataFinderPage = lazy(() => import('@/app/debug-tools/data-finder/page'))
const DataConverterPage = lazy(
  () => import('@/app/debug-tools/data-converter/page')
)
const NetworkSpeedTestPage = lazy(
  () => import('@/app/debug-tools/network-speed-test/page')
)
const DeviceDiscoveryPage = lazy(
  () => import('@/app/debug-tools/device-discovery/page')
)
const LoggerTestPage = lazy(
  () => import('@/app/debug-tools/logger-test/page')
)
const StorageManagerPage = lazy(
  () => import('@/app/debug-tools/storage-manager/page')
)
const LogAnalysisPage = lazy(
  () => import('@/app/debug-tools/log-analysis/page')
)
const DebugToolsSharedPage = lazy(() => import('@/app/debug-tools/shared/page'))

// 404页面
const NotFoundPage = lazy(() => import('@/app/not-found'))

// 加载组件
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
  </div>
)

export function AppRouter() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        {/* 主要页面路由 - 指向修改后的 app 目录文件 */}
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />

        {/* 设备管理模块路由 */}
        <Route path="/devices" element={<DevicesPage />} />
        <Route path="/devices/add" element={<DeviceAddPage />} />
        <Route path="/devices/edit/:id" element={<DeviceEditPage />} />
        <Route path="/devices/:id/details" element={<DeviceDetailsPage />} />
        <Route path="/devices/:id/tags" element={<DeviceTagsPage />} />
        <Route path="/devices/:id/messages" element={<DeviceMessagesPage />} />
        <Route path="/devices/:id/logs" element={<DeviceLogsPage />} />
        <Route
          path="/devices/:id/write-tags"
          element={<DeviceWriteTagsPage />}
        />
        <Route path="/devices/tags" element={<AllTagsPage />} />
        <Route path="/devices/alarms" element={<DeviceAlarmsPage />} />
        <Route path="/devices/events" element={<DeviceEventsPage />} />
        <Route path="/devices/groups" element={<DeviceGroupsPage />} />
        <Route path="/devices/templates" element={<DeviceTemplatesPage />} />
        <Route
          path="/devices/data-collection"
          element={<DataCollectionPage />}
        />
        <Route path="/devices/ai-demo" element={<AILabelDemoPage />} />

        {/* 数据转发模块路由 */}
        <Route path="/data-forwarding" element={<DataForwardingPage />} />
        <Route
          path="/data-forwarding/add"
          element={<DataForwardingAddPage />}
        />
        <Route
          path="/data-forwarding/:id"
          element={<DataForwardingDetailPage />}
        />
        <Route
          path="/data-forwarding/offline-data"
          element={<OfflineDataPage />}
        />

        {/* 系统设置模块路由 */}
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/settings/auth" element={<SettingsAuthPage />} />
        <Route path="/settings/config" element={<SettingsConfigPage />} />
        <Route path="/settings/params" element={<SettingsParamsPage />} />
        <Route path="/settings/update" element={<SettingsUpdatePage />} />
        <Route path="/settings/network" element={<SettingsNetworkPage />} />
        <Route path="/settings/services" element={<SettingsServicesPage />} />
        <Route path="/settings/info" element={<SettingsInfoPage />} />
        <Route path="/settings/alerts" element={<SettingsAlertsPage />} />
        <Route path="/settings/branding" element={<SettingsBrandingPage />} />
        <Route path="/settings/logs" element={<SettingsLogsPage />} />

        {/* 系统管理模块路由 */}
        <Route path="/system" element={<SystemPage />} />
        <Route path="/system/users" element={<SystemUsersPage />} />
        <Route path="/system/roles" element={<SystemRolesPage />} />
        <Route path="/system/menus" element={<SystemMenusPage />} />
        <Route path="/system/permissions" element={<PermissionsSimplePage />} />
        <Route path="/system/online-users" element={<OnlineUsersSimplePage />} />
        <Route path="/system/logs" element={<LogsSimplePage />} />
        <Route path="/system/permissions-simple" element={<PermissionsSimplePage />} />

        {/* 个人资料、许可证和帮助页面路由 */}
        <Route path="/profile" element={<ProfilePage />} />
        <Route path="/license" element={<LicensePage />} />
        <Route path="/help" element={<HelpPage />} />

        {/* 监控模块路由 */}
        <Route path="/monitoring" element={<MonitoringPage />} />
        <Route path="/monitoring/database" element={<MonitoringDatabasePage />} />

        {/* 任务中心模块路由 */}
        <Route path="/task-center" element={<TaskCenterPage />} />
        <Route path="/task-center/history" element={<TaskCenterHistoryPage />} />
        <Route path="/task-center/scheduled-tasks" element={<TaskCenterScheduledTasksPage />} />
        <Route path="/task-center/task-config" element={<TaskCenterTaskConfigPage />} />
        <Route path="/task-center/monitoring" element={<TaskCenterMonitoringPage />} />

        {/* 工作流编排模块路由 */}
        <Route path="/workflows" element={<WorkflowsPage />} />
        <Route path="/workflows/new" element={<WorkflowNewPage />} />
        <Route path="/workflows/editor" element={<WorkflowEditorPage />} />
        <Route path="/workflows/blank" element={<WorkflowBlankPage />} />
        <Route
          path="/workflows/monitoring"
          element={<WorkflowMonitoringPage />}
        />
        <Route
          path="/workflows/templates"
          element={<WorkflowTemplatesPage />}
        />
        {/* 工作流动态路由 - 用于特定工作流的详情、编辑、运行历史等 */}
        {/* <Route path="/workflows/:id" element={<WorkflowDetailPage />} /> */}
        {/* <Route path="/workflows/:id/edit" element={<WorkflowEditPage />} /> */}
        {/* <Route path="/workflows/:id/runs" element={<WorkflowRunsPage />} /> */}

        {/* 调试工具模块路由 - 使用嵌套路由统一布局 */}
        <Route path="/debug-tools" element={<DebugToolsPage />}>
          {/* 子路由会被渲染在父路由的 <Outlet /> 位置 */}
          <Route path="http-test" element={<HttpTestPage />} />
          <Route path="ping-test" element={<PingTestPage />} />
          <Route path="port-test" element={<PortTestPage />} />
          <Route path="packet-capture" element={<PacketCapturePage />} />
          <Route path="tcp-test" element={<TcpTestPage />} />
          <Route path="serial-debug" element={<SerialDebugPage />} />
          <Route path="mqtt-test" element={<MqttTestPage />} />
          <Route path="websocket-test" element={<WebSocketTestPage />} />
          <Route path="webssh" element={<WebSSHPage />} />
          <Route path="protocol-analysis" element={<ProtocolAnalysisPage />} />
          <Route path="json-formatter" element={<JsonFormatterPage />} />
          <Route
            path="timestamp-converter"
            element={<TimestampConverterPage />}
          />
          <Route path="data-finder" element={<DataFinderPage />} />
          <Route path="data-converter" element={<DataConverterPage />} />
          <Route path="network-speed-test" element={<NetworkSpeedTestPage />} />
          <Route path="device-discovery" element={<DeviceDiscoveryPage />} />
          <Route path="log-analysis" element={<LogAnalysisPage />} />
          <Route path="logger-test" element={<LoggerTestPage />} />
          <Route path="storage-manager" element={<StorageManagerPage />} />
          <Route path="shared" element={<DebugToolsSharedPage />} />
        </Route>

        {/* 404页面 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Suspense>
  )
}
