/**
 * 权限配置页面
 *
 * 菜单位置：权限管理 > 权限配置
 * 路由地址：/system/permissions
 * 页面功能：配置系统权限策略，包括功能权限、数据权限、API权限等
 *
 * 技术特点：
 * - 权限树形结构管理
 * - 权限继承和覆盖机制
 * - 细粒度权限控制
 * - 权限模板和预设
 *
 * 业务价值：
 * - 提供灵活的权限配置能力
 * - 支持复杂的权限策略
 * - 简化权限管理流程
 * - 确保系统安全性
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  permissionManagementApi,
  SysPermission,
  PermissionTemplate,
  PermissionInput,
  PermissionTemplateInput
} from '@/lib/api/permission-management-api'
import { feature, LoadingManager } from '@/lib/api-services/axios-utils'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Key,
  Search,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Settings,
  Shield,
  Database,
  Globe,
  FileText,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  ChevronRight,
  ChevronDown,
  Menu,
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

// 加载状态键
const LOADING_KEY = {
  PERMISSION_LIST: 'permissions.list',
  PERMISSION_CREATE: 'permissions.create',
  PERMISSION_UPDATE: 'permissions.update',
  PERMISSION_DELETE: 'permissions.delete',
  TEMPLATE_LIST: 'permissions.templates',
  TEMPLATE_CREATE: 'permissions.template.create',
  TEMPLATE_UPDATE: 'permissions.template.update',
  TEMPLATE_DELETE: 'permissions.template.delete',
}

export default function PermissionsPage() {
  const { toast } = useToast()
  const [permissions, setPermissions] = useState<SysPermission[]>([])
  const [templates, setTemplates] = useState<PermissionTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false)
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false)
  const [editingPermission, setEditingPermission] = useState<SysPermission | null>(null)
  const [editingTemplate, setEditingTemplate] = useState<PermissionTemplate | null>(null)
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  // 表单状态
  const [permissionForm, setPermissionForm] = useState({
    name: '',
    code: '',
    type: 'function' as 'module' | 'function' | 'data' | 'api',
    description: '',
    parentId: null as number | null,
    enabled: true,
    visible: true,
    sort: 0,
  })

  const [templateForm, setTemplateForm] = useState({
    name: '',
    description: '',
    permissions: [] as number[],
    type: 'custom' as 'system' | 'custom',
  })

  // 加载权限树数据
  const loadPermissions = async () => {
    try {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_LIST, true)

      const [error, response] = await feature(
        permissionManagementApi.getPermissionTree({
          name: searchTerm || undefined,
          type: selectedType === 'all' ? undefined : selectedType,
        })
      )

      if (error) {
        console.error('获取权限列表失败:', error)
        toast({
          title: '加载失败',
          description: '无法获取权限列表，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      if (response?.data?.data) {
        setPermissions(response.data.data)
      }
    } catch (error) {
      console.error('获取权限列表异常:', error)
      toast({
        title: '系统错误',
        description: '获取权限列表时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_LIST, false)
      setLoading(false)
    }
  }

  // 加载权限模板数据
  const loadTemplates = async () => {
    try {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_LIST, true)

      const [error, response] = await feature(
        permissionManagementApi.getPermissionTemplates()
      )

      if (error) {
        console.error('获取权限模板失败:', error)
        toast({
          title: '加载失败',
          description: '无法获取权限模板，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      if (response?.data?.data) {
        setTemplates(response.data.data)
      }
    } catch (error) {
      console.error('获取权限模板异常:', error)
      toast({
        title: '系统错误',
        description: '获取权限模板时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_LIST, false)
    }
  }

  useEffect(() => {
    loadPermissions()
    loadTemplates()
  }, [])



  // 搜索和过滤变化时重新加载
  useEffect(() => {
    const timer = setTimeout(() => {
      loadPermissions()
    }, 500)

    return () => clearTimeout(timer)
  }, [searchTerm, selectedType])

  const handleCreatePermission = () => {
    setEditingPermission(null)
    setPermissionForm({
      name: '',
      code: '',
      type: 'function',
      description: '',
      parentId: null,
      enabled: true,
      visible: true,
      sort: 0,
    })
    setIsPermissionDialogOpen(true)
  }

  const handleEditPermission = (permission: SysPermission) => {
    setEditingPermission(permission)
    setPermissionForm({
      name: permission.name,
      code: permission.code,
      type: permission.type,
      description: permission.description || '',
      parentId: permission.parentId || null,
      enabled: permission.enabled,
      visible: permission.visible,
      sort: permission.sort,
    })
    setIsPermissionDialogOpen(true)
  }

  const handleSavePermission = async () => {
    if (!permissionForm.name.trim() || !permissionForm.code.trim()) {
      toast({
        title: '验证失败',
        description: '权限名称和编码不能为空',
        variant: 'destructive',
      })
      return
    }

    try {
      if (editingPermission) {
        // 更新权限
        LoadingManager.setLoading(LOADING_KEY.PERMISSION_UPDATE, true)

        const [error] = await feature(
          permissionManagementApi.updatePermission(editingPermission.id, permissionForm)
        )

        if (error) {
          console.error('更新权限失败:', error)
          toast({
            title: '更新失败',
            description: '无法更新权限，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '更新成功',
          description: `权限 "${permissionForm.name}" 已更新`,
        })
      } else {
        // 创建权限
        LoadingManager.setLoading(LOADING_KEY.PERMISSION_CREATE, true)

        const [error] = await feature(
          permissionManagementApi.createPermission(permissionForm)
        )

        if (error) {
          console.error('创建权限失败:', error)
          toast({
            title: '创建失败',
            description: '无法创建权限，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '创建成功',
          description: `权限 "${permissionForm.name}" 已创建`,
        })
      }

      setIsPermissionDialogOpen(false)
      loadPermissions()
    } catch (error) {
      console.error('保存权限异常:', error)
      toast({
        title: '系统错误',
        description: '保存权限时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(editingPermission ? LOADING_KEY.PERMISSION_UPDATE : LOADING_KEY.PERMISSION_CREATE, false)
    }
  }

  const handleDeletePermission = async (permission: SysPermission) => {
    if (!confirm(`确定要删除权限 "${permission.name}" 吗？`)) {
      return
    }

    try {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_DELETE, true)

      const [error] = await feature(
        permissionManagementApi.deletePermission(permission.id)
      )

      if (error) {
        console.error('删除权限失败:', error)
        toast({
          title: '删除失败',
          description: '无法删除权限，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: '删除成功',
        description: `权限 "${permission.name}" 已删除`,
      })

      loadPermissions()
    } catch (error) {
      console.error('删除权限异常:', error)
      toast({
        title: '系统错误',
        description: '删除权限时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_DELETE, false)
    }
  }

  const handleCreateTemplate = () => {
    setEditingTemplate(null)
    setTemplateForm({
      name: '',
      description: '',
      permissions: [],
      type: 'custom',
    })
    setIsTemplateDialogOpen(true)
  }

  const handleEditTemplate = (template: PermissionTemplate) => {
    setEditingTemplate(template)
    setTemplateForm({
      name: template.name,
      description: template.description,
      permissions: template.permissions,
      type: template.type,
    })
    setIsTemplateDialogOpen(true)
  }

  const handleSaveTemplate = async () => {
    if (!templateForm.name.trim()) {
      toast({
        title: '验证失败',
        description: '模板名称不能为空',
        variant: 'destructive',
      })
      return
    }

    try {
      if (editingTemplate) {
        // 更新模板
        LoadingManager.setLoading(LOADING_KEY.TEMPLATE_UPDATE, true)

        const [error] = await feature(
          permissionManagementApi.updatePermissionTemplate(editingTemplate.id, templateForm)
        )

        if (error) {
          console.error('更新权限模板失败:', error)
          toast({
            title: '更新失败',
            description: '无法更新权限模板，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '更新成功',
          description: `权限模板 "${templateForm.name}" 已更新`,
        })
      } else {
        // 创建模板
        LoadingManager.setLoading(LOADING_KEY.TEMPLATE_CREATE, true)

        const [error] = await feature(
          permissionManagementApi.createPermissionTemplate(templateForm)
        )

        if (error) {
          console.error('创建权限模板失败:', error)
          toast({
            title: '创建失败',
            description: '无法创建权限模板，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '创建成功',
          description: `权限模板 "${templateForm.name}" 已创建`,
        })
      }

      setIsTemplateDialogOpen(false)
      loadTemplates()
    } catch (error) {
      console.error('保存权限模板异常:', error)
      toast({
        title: '系统错误',
        description: '保存权限模板时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(editingTemplate ? LOADING_KEY.TEMPLATE_UPDATE : LOADING_KEY.TEMPLATE_CREATE, false)
    }
  }

  const handleDeleteTemplate = async (template: PermissionTemplate) => {
    if (!confirm(`确定要删除权限模板 "${template.name}" 吗？`)) {
      return
    }

    try {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_DELETE, true)

      const [error] = await feature(
        permissionManagementApi.deletePermissionTemplate(template.id)
      )

      if (error) {
        console.error('删除权限模板失败:', error)
        toast({
          title: '删除失败',
          description: '无法删除权限模板，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: '删除成功',
        description: `权限模板 "${template.name}" 已删除`,
      })

      loadTemplates()
    } catch (error) {
      console.error('删除权限模板异常:', error)
      toast({
        title: '系统错误',
        description: '删除权限模板时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_DELETE, false)
    }
  }

  const toggleNode = (nodeId: number | string) => {
    const nodeIdStr = nodeId.toString()
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeIdStr)) {
      newExpanded.delete(nodeIdStr)
    } else {
      newExpanded.add(nodeIdStr)
    }
    setExpandedNodes(newExpanded)
  }

  const getPermissionTypeIcon = (type: SysPermission['type']) => {
    switch (type) {
      case 'module':
        return <Shield className="h-4 w-4" />
      case 'function':
        return <Key className="h-4 w-4" />
      case 'data':
        return <Database className="h-4 w-4" />
      case 'api':
        return <Globe className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getPermissionTypeBadge = (type: SysPermission['type']) => {
    const variants = {
      module: 'default',
      function: 'secondary',
      data: 'outline',
      api: 'destructive',
    } as const

    const labels = {
      module: '模块',
      function: '功能',
      data: '数据',
      api: 'API',
    }

    return (
      <Badge variant={variants[type]}>
        {labels[type]}
      </Badge>
    )
  }

  const renderPermissionTree = (items: SysPermission[], level = 0) => {
    return items.map((permission) => (
      <div key={permission.id}>
        <TableRow className="hover:bg-muted/50">
          <TableCell>
            <div className="flex items-center" style={{ paddingLeft: `${level * 20}px` }}>
              {permission.children && permission.children.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 mr-2"
                  onClick={() => toggleNode(permission.id!)}>
                  {expandedNodes.has(permission.id!.toString()) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              )}
              <div className="flex items-center space-x-2">
                {getPermissionTypeIcon(permission.type)}
                <span className="font-medium">{permission.name}</span>
              </div>
            </div>
          </TableCell>
          <TableCell>
            <code className="text-xs bg-muted px-2 py-1 rounded">
              {permission.code}
            </code>
          </TableCell>
          <TableCell>{getPermissionTypeBadge(permission.type)}</TableCell>
          <TableCell className="max-w-xs truncate">
            {permission.description}
          </TableCell>
          <TableCell>
            {permission.enabled ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <Unlock className="h-3 w-3 mr-1" />
                启用
              </Badge>
            ) : (
              <Badge variant="secondary">
                <Lock className="h-3 w-3 mr-1" />
                禁用
              </Badge>
            )}
          </TableCell>
          <TableCell>
            {permission.visible ? (
              <Eye className="h-4 w-4 text-green-600" />
            ) : (
              <EyeOff className="h-4 w-4 text-gray-400" />
            )}
          </TableCell>
          <TableCell>{permission.sort}</TableCell>
          <TableCell>{permission.createTime}</TableCell>
          <TableCell>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleEditPermission(permission)}>
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDeletePermission(permission)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </TableCell>
        </TableRow>
        {permission.children && 
         expandedNodes.has(permission.id) && 
         renderPermissionTree(permission.children, level + 1)}
      </div>
    ))
  }

  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = permission.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.code?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || permission.type === selectedType
    return matchesSearch && matchesType
  })

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">权限配置</h1>
          <p className="text-muted-foreground">
            配置系统权限策略，管理功能权限、数据权限和API权限
          </p>
        </div>

        {/* 权限模板卡片 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>权限模板</CardTitle>
                <CardDescription>预定义的权限配置模板</CardDescription>
              </div>
              <Button onClick={handleCreateTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                新建模板
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {templates.map((template) => (
                <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{template.name}</CardTitle>
                      <Badge variant={template.type === 'system' ? 'default' : 'secondary'}>
                        {template.type === 'system' ? '系统' : '自定义'}
                      </Badge>
                    </div>
                    <CardDescription className="text-sm">
                      {template.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        {template.permissions.length} 个权限
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditTemplate(template)}>
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 权限列表 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>权限列表</CardTitle>
                <CardDescription>系统中所有权限的层级结构</CardDescription>
              </div>
              <Button onClick={handleCreatePermission}>
                <Plus className="h-4 w-4 mr-2" />
                添加权限
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-6">
              <div className="flex items-center space-x-2 flex-1">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索权限..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="module">模块</SelectItem>
                  <SelectItem value="function">功能</SelectItem>
                  <SelectItem value="data">数据</SelectItem>
                  <SelectItem value="api">API</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                onClick={() => {
                  setLoading(true)
                  loadPermissions()
                  loadTemplates()
                }}
                disabled={LoadingManager.isLoading(LOADING_KEY.PERMISSION_LIST)}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${LoadingManager.isLoading(LOADING_KEY.PERMISSION_LIST) ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                <span>加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>权限名称</TableHead>
                    <TableHead>权限编码</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>可见</TableHead>
                    <TableHead>排序</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {renderPermissionTree(filteredPermissions)}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 权限编辑对话框 */}
        <Dialog open={isPermissionDialogOpen} onOpenChange={setIsPermissionDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingPermission ? '编辑权限' : '添加权限'}
              </DialogTitle>
              <DialogDescription>
                配置权限的基本信息和属性
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="permissionName" className="text-right">
                  权限名称
                </Label>
                <Input
                  id="permissionName"
                  value={permissionForm.name}
                  onChange={(e) => setPermissionForm({ ...permissionForm, name: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入权限名称"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="permissionCode" className="text-right">
                  权限编码
                </Label>
                <Input
                  id="permissionCode"
                  value={permissionForm.code}
                  onChange={(e) => setPermissionForm({ ...permissionForm, code: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入权限编码"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="permissionType" className="text-right">
                  权限类型
                </Label>
                <Select
                  value={permissionForm.type}
                  onValueChange={(value) => setPermissionForm({ ...permissionForm, type: value as any })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择权限类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="module">模块</SelectItem>
                    <SelectItem value="function">功能</SelectItem>
                    <SelectItem value="data">数据</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="permissionDescription" className="text-right">
                  描述
                </Label>
                <Textarea
                  id="permissionDescription"
                  value={permissionForm.description}
                  onChange={(e) => setPermissionForm({ ...permissionForm, description: e.target.value })}
                  className="col-span-3"
                  rows={3}
                  placeholder="请输入权限描述"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="permissionSort" className="text-right">
                  排序
                </Label>
                <Input
                  id="permissionSort"
                  type="number"
                  value={permissionForm.sort}
                  onChange={(e) => setPermissionForm({ ...permissionForm, sort: parseInt(e.target.value) || 0 })}
                  className="col-span-3"
                  placeholder="请输入排序值"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPermissionDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleSavePermission}
                disabled={LoadingManager.isLoading(LOADING_KEY.PERMISSION_CREATE) || LoadingManager.isLoading(LOADING_KEY.PERMISSION_UPDATE)}
              >
                {LoadingManager.isLoading(LOADING_KEY.PERMISSION_CREATE) || LoadingManager.isLoading(LOADING_KEY.PERMISSION_UPDATE)
                  ? '保存中...'
                  : (editingPermission ? '保存' : '创建')
                }
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 权限模板对话框 */}
        <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingTemplate ? '编辑权限模板' : '添加权限模板'}
              </DialogTitle>
              <DialogDescription>
                配置权限模板的基本信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="templateName" className="text-right">
                  模板名称
                </Label>
                <Input
                  id="templateName"
                  value={templateForm.name}
                  onChange={(e) => setTemplateForm({ ...templateForm, name: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入模板名称"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="templateDescription" className="text-right">
                  描述
                </Label>
                <Textarea
                  id="templateDescription"
                  value={templateForm.description}
                  onChange={(e) => setTemplateForm({ ...templateForm, description: e.target.value })}
                  className="col-span-3"
                  rows={3}
                  placeholder="请输入模板描述"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsTemplateDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleSaveTemplate}
                disabled={LoadingManager.isLoading(LOADING_KEY.TEMPLATE_CREATE) || LoadingManager.isLoading(LOADING_KEY.TEMPLATE_UPDATE)}
              >
                {LoadingManager.isLoading(LOADING_KEY.TEMPLATE_CREATE) || LoadingManager.isLoading(LOADING_KEY.TEMPLATE_UPDATE)
                  ? '保存中...'
                  : (editingTemplate ? '保存' : '创建')
                }
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
